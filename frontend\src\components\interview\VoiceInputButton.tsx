import React, { useState, useRef, useCallback, useEffect } from 'react';
import useAuthStore from '../../stores/authStore';

interface VoiceInputButtonProps {
  onTextReceived: (text: string) => void;
  disabled?: boolean;
  className?: string;
  webSocket?: WebSocket | null; // 接收现有的WebSocket连接
  onSendMessage?: (message: any) => void; // 发送消息的回调
}

interface RecognitionResult {
  text: string;
  confidence: number;
  isFinal: boolean;
}

/**
 * 语音输入按钮组件
 * 集成ASR服务，支持点击录音和语音转文字
 */
const VoiceInputButton: React.FC<VoiceInputButtonProps> = ({
  onTextReceived,
  disabled = false,
  className = '',
  webSocket = null,
  onSendMessage = null
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  // 音频相关引用
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  const recordingStartTimeRef = useRef<number>(0);

  /**
   * 检查浏览器支持和权限
   */
  const checkPermissions = useCallback(async () => {
    try {
      // 检查浏览器支持
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('您的浏览器不支持语音录制功能');
      }

      // 请求麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop()); // 立即停止，只是检查权限
      setHasPermission(true);
      setError(null);
      return true;
    } catch (err: any) {
      console.error('权限检查失败:', err);
      setHasPermission(false);
      if (err.name === 'NotAllowedError') {
        setError('请允许访问麦克风以使用语音输入功能');
      } else if (err.name === 'NotFoundError') {
        setError('未检测到麦克风设备');
      } else {
        setError(err.message || '无法访问麦克风');
      }
      return false;
    }
  }, []);

  /**
   * 检查WebSocket连接是否可用
   */
  const isWebSocketAvailable = useCallback(() => {
    // 如果有webSocket prop，检查其状态
    if (webSocket) {
      return webSocket.readyState === WebSocket.OPEN;
    }
    // 如果没有webSocket prop但有onSendMessage，说明使用WebSocketProvider
    return !!onSendMessage;
  }, [webSocket, onSendMessage]);

  /**
   * 处理WebSocket消息
   */
  const handleWebSocketMessage = useCallback((data: any) => {
    if (data.type === 'transcription') {
      const result: RecognitionResult = {
        text: data.text || '',
        confidence: data.confidence || 0,
        isFinal: !data.isPartial
      };

      // 只处理最终结果，避免部分结果的干扰
      if (result.isFinal && result.text.trim()) {
        console.log('🎯 收到最终语音识别结果:', result.text);
        onTextReceived(result.text.trim());
        setIsProcessing(false);
      }
    } else if (data.type === 'asr_error') {
      console.error('ASR错误:', data.error);
      setError('语音识别失败，请重试');
      setIsProcessing(false);
    }
  }, [onTextReceived]);

  // 监听WebSocket消息
  useEffect(() => {
    if (!webSocket) return;

    const messageHandler = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      } catch (err) {
        console.error('解析WebSocket消息失败:', err);
      }
    };

    webSocket.addEventListener('message', messageHandler);

    return () => {
      webSocket.removeEventListener('message', messageHandler);
    };
  }, [webSocket, handleWebSocketMessage]);

  /**
   * 获取浏览器支持的音频格式
   */
  const getSupportedMimeType = useCallback(() => {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus',
      'audio/wav'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported && MediaRecorder.isTypeSupported(type)) {
        console.log('🎵 支持的音频格式:', type);
        return type;
      }
    }

    console.log('🎵 使用默认音频格式');
    return null; // 使用默认格式
  }, []);

  /**
   * 开始录音
   */
  const startRecording = useCallback(async () => {
    try {
      setError(null);
      setIsProcessing(false);

      // 检查WebSocket连接
      if (!isWebSocketAvailable()) {
        throw new Error('WebSocket连接不可用，请确保面试会话已建立');
      }

      // 检查权限
      const hasPermission = await checkPermissions();
      if (!hasPermission) return;

      // 获取音频流
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      streamRef.current = stream;

      // 检查音频轨道状态
      const audioTracks = stream.getAudioTracks();
      console.log('🎵 音频轨道数量:', audioTracks.length);
      audioTracks.forEach((track, index) => {
        console.log(`🎵 音频轨道${index}:`, {
          enabled: track.enabled,
          readyState: track.readyState,
          muted: track.muted,
          label: track.label
        });
      });

      // 获取支持的音频格式
      const supportedMimeType = getSupportedMimeType();
      const recorderOptions = supportedMimeType ? { mimeType: supportedMimeType } : {};

      console.log('🎵 MediaRecorder配置:', recorderOptions);

      // 创建MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, recorderOptions);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      // 添加MediaRecorder事件监听
      mediaRecorder.onstart = () => {
        console.log('🎤 MediaRecorder已启动');
        recordingStartTimeRef.current = Date.now();
      };

      mediaRecorder.onerror = (event) => {
        console.error('🎤 MediaRecorder错误:', event);
      };

      mediaRecorder.onpause = () => {
        console.log('🎤 MediaRecorder已暂停');
      };

      mediaRecorder.onresume = () => {
        console.log('🎤 MediaRecorder已恢复');
      };

      // 处理录音数据
      mediaRecorder.ondataavailable = (event) => {
        console.log('🎵 收到音频数据:', event.data.size, 'bytes');
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      // 录音结束处理
      mediaRecorder.onstop = () => {
        const recordingDuration = Date.now() - recordingStartTimeRef.current;
        console.log('🔇 录音结束，时长:', recordingDuration, 'ms, 音频块数量:', audioChunksRef.current.length);

        // 检查录音时长
        if (recordingDuration < 500) {
          console.warn('⚠️ 录音时间过短:', recordingDuration, 'ms');
        }

        // 使用实际的MIME类型创建Blob
        const actualMimeType = supportedMimeType || 'audio/webm';
        const audioBlob = new Blob(audioChunksRef.current, { type: actualMimeType });
        console.log('🎵 音频Blob大小:', audioBlob.size, 'bytes, 格式:', actualMimeType);

        // 检查音频轨道状态
        const audioTracks = stream.getAudioTracks();
        audioTracks.forEach((track, index) => {
          console.log(`🎵 录音结束时音频轨道${index}状态:`, {
            enabled: track.enabled,
            readyState: track.readyState,
            muted: track.muted
          });
        });

        sendAudioForRecognition(audioBlob);
      };

      // 开始录音，每1000ms收集一次数据（修复：从100ms改为1000ms）
      mediaRecorder.start(1000);
      setIsRecording(true);
      console.log('🎤 开始录音，格式:', supportedMimeType || '默认');

    } catch (err: any) {
      console.error('开始录音失败:', err);
      setError(err.message || '录音失败');
      cleanup();
    }
  }, [checkPermissions, isWebSocketAvailable, getSupportedMimeType]);

  /**
   * 停止录音
   */
  const stopRecording = useCallback(() => {
    try {
      if (mediaRecorderRef.current && isRecording) {
        const recordingDuration = Date.now() - recordingStartTimeRef.current;
        console.log('🔇 准备停止录音，当前录音时长:', recordingDuration, 'ms');

        // 检查最小录音时间
        if (recordingDuration < 1000) {
          console.warn('⚠️ 录音时间过短，建议至少录音1秒');
          setError('录音时间过短，请至少录音1秒');
          cleanup();
          return;
        }

        mediaRecorderRef.current.stop();
        setIsRecording(false);
        setIsProcessing(true);
        console.log('🔇 停止录音，开始处理...');
      }
    } catch (err) {
      console.error('停止录音失败:', err);
      setError('停止录音失败');
      cleanup();
    }
  }, [isRecording]);

  /**
   * 发送音频数据进行识别
   */
  const sendAudioForRecognition = useCallback(async (audioBlob: Blob) => {
    try {
      if (!isWebSocketAvailable()) {
        throw new Error('WebSocket连接不可用');
      }

      // 检查音频数据大小
      if (audioBlob.size === 0) {
        throw new Error('音频数据为空');
      }

      console.log('🎵 准备发送音频数据，大小:', audioBlob.size, 'bytes, 类型:', audioBlob.type);

      // 转换为ArrayBuffer
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioData = new Uint8Array(arrayBuffer);

      console.log('🔄 音频数据转换完成，字节数:', audioData.length);

      // 转换为base64编码
      const base64Audio = btoa(String.fromCharCode(...audioData));

      console.log('🔄 Base64编码完成，长度:', base64Audio.length);

      // 从Blob类型中提取格式信息
      let format = 'webm'; // 默认格式
      if (audioBlob.type) {
        if (audioBlob.type.includes('webm')) {
          format = 'webm';
        } else if (audioBlob.type.includes('mp4')) {
          format = 'mp4';
        } else if (audioBlob.type.includes('ogg')) {
          format = 'ogg';
        } else if (audioBlob.type.includes('wav')) {
          format = 'wav';
        }
      }

      // 构建音频消息
      const audioMessage = {
        type: 'audio_chunk',
        payload: base64Audio,
        format: format,
        sampleRate: 16000,
        channels: 1,
        timestamp: Date.now(),
        mode: 'voice'
      };

      console.log('📤 准备发送音频消息，格式:', format);

      // 使用传入的发送消息函数或直接发送
      if (onSendMessage) {
        onSendMessage(audioMessage);
      } else if (webSocket) {
        webSocket.send(JSON.stringify(audioMessage));
      }

      console.log('📤 音频数据已发送，等待识别结果...');
    } catch (err) {
      console.error('发送音频数据失败:', err);
      setError('语音识别失败');
      setIsProcessing(false);
    }
  }, [isWebSocketAvailable, onSendMessage, webSocket]);

  /**
   * 清理资源
   */
  const cleanup = useCallback(() => {
    // 停止录音
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
    }

    // 关闭音频流
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // 重置状态
    setIsRecording(false);
    setIsProcessing(false);
    audioChunksRef.current = [];
    mediaRecorderRef.current = null;
  }, [isRecording]);

  /**
   * 切换录音状态
   */
  const toggleRecording = useCallback(() => {
    if (disabled) return;

    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  }, [disabled, isRecording, startRecording, stopRecording]);

  // 组件卸载时清理资源
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // 获取按钮样式
  const getButtonStyle = () => {
    if (disabled || hasPermission === false) {
      return 'bg-transparent text-gray-400 cursor-not-allowed border border-gray-300';
    }
    if (isRecording) {
      return 'bg-red-500 text-white animate-pulse hover:bg-red-600 border border-red-500';
    }
    if (isProcessing) {
      return 'bg-yellow-500 text-white border border-yellow-500';
    }
    return 'bg-transparent text-gray-600 hover:text-blue-600 hover:bg-blue-50 border border-gray-300';
  };

  // 获取按钮图标
  const getButtonIcon = () => {
    if (isProcessing) {
      return (
        <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      );
    }

    if (isRecording) {
      return (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
          <rect x="6" y="4" width="4" height="16" rx="2" />
          <rect x="14" y="4" width="4" height="16" rx="2" />
        </svg>
      );
    }

    return (
      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" />
        <path d="M19 10v2a7 7 0 0 1-14 0v-2" stroke="currentColor" strokeWidth="2" fill="none" />
        <line x1="12" y1="19" x2="12" y2="23" stroke="currentColor" strokeWidth="2" />
        <line x1="8" y1="23" x2="16" y2="23" stroke="currentColor" strokeWidth="2" />
      </svg>
    );
  };

  // 获取提示文本
  const getTooltipText = () => {
    if (disabled) return '语音输入不可用';
    if (hasPermission === false) return error || '无法访问麦克风';
    if (isRecording) return '点击停止录音';
    if (isProcessing) return '正在识别语音...';
    return '点击开始语音输入';
  };

  return (
    <div className="relative">
      <button
        onClick={toggleRecording}
        disabled={disabled || hasPermission === false}
        className={`p-2 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 ${getButtonStyle()} ${className}`}
        title={getTooltipText()}
        aria-label={getTooltipText()}
      >
        {getButtonIcon()}
      </button>

      {/* 错误提示 */}
      {error && (
        <div className="absolute top-full left-0 mt-1 p-2 bg-red-100 text-red-700 text-xs rounded shadow-lg whitespace-nowrap z-10">
          {error}
        </div>
      )}
    </div>
  );
};

export default VoiceInputButton;
