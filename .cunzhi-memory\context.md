# 项目上下文信息

- AI模拟面试功能重构需求：新增开始按钮和实时对话流程，使用LLM实现面试官-用户问答循环，严格保持现有UI不变
- 用户要求输出git提交命令进行手动提交，需要使用"开发版："前缀，并完整记录当前的改动内容。例如：“# 添加所有修改的文件
git add .

# 提交所有更改
git commit -m "开发版：重构支付宝支付功能，集成易支付API实现真实支付 

🎯 主要功能：
- 后端集成易支付API (z-pay.cn)，支持真实支付宝支付
- 创建AlipayService支付服务类，实现MD5签名算法
- 支持API接口方式获取支付二维码
- 新增支付回调处理和订单状态查询

📁 后端更改：
- 新增 backend/services/alipayService.ts - 支付宝支付服务
- 新增 backend/routes/payments.ts - 支付相关路由
- 修改 backend/routes/orders.ts - 集成支付宝API调用
- 修改 backend/server.ts - 注册支付路由
- 安装 axios 依赖

🎨 前端更改：
- 修改 frontend/src/pages/PricingPage.tsx - 添加跳转Toast提示
- 修改 frontend/src/pages/PaymentQRPage.tsx - 显示真实二维码和加载提示
- 新增 frontend/src/pages/PaymentSuccessPage.tsx - 支付结果页面
- 修改 frontend/src/App.tsx - 添加支付成功页面路由

✨ 用户体验优化：
- 支付流程Toast提示：跳转提示 + 二维码生成提示
- 显示真实支付宝二维码，支持扫码支付
- 详细订单信息展示（订单号、创建时间、支付状态等）
- 支付成功页面，完整的支付结果处理

🔧 技术实现：
- MD5签名验证，确保支付安全
- 支付回调处理，自动更新订单状态和用户余额
- 事务处理，保证数据一致性
- 防重复处理，避免重复扣费
"”
- 后端服务器启动问题解决方案：当前端出现500错误且无法连接API时，首先检查后端服务器是否在运行。使用 `netstat -ano | findstr ":3000" | findstr "LISTENING"` 检查端口状态，如果没有LISTENING状态则需要启动后端服务器。在backend目录下使用 `npx tsx watch server.ts` 启动开发服务器，确保数据库连接和WebSocket服务正常初始化。
- 修复分享有礼页面邀请码和邀请链接复制功能Toast提示不显示问题：将ReferralRewardsPage.tsx中的useToast hook改为useToastContext，修复了Toast状态隔离导致的显示问题，现在复制按钮点击后能正常显示绿色成功Toast提示
- 修复管理系统Toast进度条问题：1.修复ToastContext.tsx中ToastContainer缺少toasts和onRemove参数传递的问题 2.将Toast.tsx中复杂的JavaScript+requestAnimationFrame进度条实现改为简单可靠的CSS动画方式，与主系统保持一致
- 通知系统测试已完成：管理后台成功创建通知"测试通知功能"，通知被正确保存到数据库，前端成功从API获取并显示通知列表，createUserNotifications函数被正确调用。用户已登录主系统准备测试通知接收功能。
- 通知管理系统开发完成：实现了完整的管理后台通知CRUD功能、用户通知接收机制、主系统通知显示界面，包括数据库schema设计(Notification/NotificationTarget/UserNotification表)、后端API路由、前端管理界面和通知下拉组件，已通过完整测试验证功能正常
- AI模拟面试添加15秒倒计时功能：位置1在开始面试按钮"正在启动..."旁显示倒计时，位置2在实时页面等待文案处显示倒计时，纯前端UI优化不影响后端逻辑，文案改为"面试君正在分析您的简历，为您量身定制专业面试问题... 请稍等片刻，精彩的面试体验即将开始！"
- AI模拟面试倒计时功能完成：位置1开始面试按钮倒计时10秒，位置2实时页面倒计时15秒，输入栏高度调整为原来1/3，文案优化为"面试君正在分析您的简历，为您量身定制专业面试问题... 请稍等片刻，精彩的面试体验即将开始！"
- 用户在PricingPage.tsx中修复了充值中心页面的UI对齐问题，将上下两个白色卡片的宽度从max-w-2xl调整为max-w-xl，确保"面巾购买"卡片和"兑换规则"卡片的左右边缘完全对齐
- 完成模拟面试系统真实岗位数据连接功能：修改backend/websocket/handlers/mockInterviewService.ts，在startMockInterview方法中添加数据库更新逻辑，新增updateSessionJobInfo私有方法，解决"未知公司"和"Job Info from Client"模拟数据问题，实现前端岗位选择到数据库titleJobInfo字段的完整数据流连接
- 完成AI正式面试岗位信息连接修复：1)后端添加sessionManager.ts的updateSessionJobInfo方法和messageHandler.ts的update_job_info消息处理；2)前端修改useInterviewSession.ts添加sendTextMessage方法，LiveInterviewPage.tsx添加WebSocket岗位信息发送逻辑；3)修复面试记录时间显示问题，将UTC时间转换为Asia/Shanghai本地时间；4)解决AI正式面试"未知公司"显示问题，实现真实岗位数据传递
- MianshiJun项目已成功部署到生产服务器*************。后端服务通过PM2运行在端口3000，前端通过Nginx在端口80提供服务。API代理和WebSocket代理已配置。用户希望配置SSL证书以启用HTTPS。
- MianshiJun项目已完全部署完成，包括HTTPS SSL证书配置。网站可通过 https://mianshijun.xyz 访问，所有服务正常运行，HTTP自动重定向到HTTPS，证书自动续期已配置。
- 服务器部署问题：backend/routes/auth.ts中VerificationService导入路径大小写错误，需要从'../services/VerificationService'改为'../services/verificationService'
- 遇到部署问题时，一律1.本地修复 → 2. 推送GitHub → 3. 服务器拉取。- 项目是monorepo结构，包含packages/common包。common包是私有包，需要在服务器上配置workspace或构建后上传。前端依赖@new-mianshijun/common导致npm install失败。
- 本次更新完善了邀请码系统的用户体验和功能完整性：1.修复showToast错误，统一使用Toast组件；2.修复邀请码生成包含特殊字符问题，使用纯数字字母；3.优化邀请人邀请码填写UI，保持布局不变，从输入状态转换为显示状态；4.增强后端API返回邀请人邀请码信息；5.修复兑换功能事务超时问题
- 修复邀请记录卡片滚动功能：设置max-h-[300px]限制高度，确保左右卡片高度一致，当邀请记录超过2-3条时自动显示滚动条，解决了内容溢出和高度不一致问题
- 支付宝支付功能重构完成：1.后端集成易支付API(z-pay.cn)，实现真实支付宝支付；2.创建AlipayService支付服务，支持MD5签名和API调用；3.修改订单创建流程，集成支付二维码生成；4.新增支付回调处理和状态查询API；5.前端优化支付流程，显示真实二维码；6.添加Toast提示改善用户体验；7.创建PaymentSuccessPage处理支付结果
- 服务器SSH配置状态：1)SSH服务正常运行，2)当前配置：PermitRootLogin yes，PasswordAuthentication no（仅允许密钥认证），3)admin用户已有authorized_keys文件，4)服务器IP：*************，5)当前通过阿里云控制台连接，需要配置本地SSH密钥登录
- SSH密钥配置完成：1)本地电脑SSH密钥已成功添加到服务器authorized_keys，2)可以直接使用ssh admin@*************从本地连接服务器，3)无需密码认证，4)下一步需要配置GitHub专用代理：hysteria2://1199a0b6@**************:4091/?insecure=1&sni=www.bing.com#Hysteria2-misaka
- GitHub代理配置完成：1)Hysteria2客户端安装并配置为开机自启动服务，2)Git配置仅对GitHub使用代理(http://127.0.0.1:8080)，3)代理测试成功-可正常访问GitHub，4)创建github-proxy管理脚本(/usr/local/bin/github-proxy)支持status和restart命令，5)服务器IP:*************，SSH密钥认证正常工作
- 项目部署完成：1)服务器IP:*************，2)后端服务运行在3000端口(PM2管理)，3)前端服务运行在4173端口(PM2管理)，4)Redis服务正常运行，5)使用Neon PostgreSQL云数据库，6)GitHub代理正常工作，7)需要配置Nginx反向代理绑定域名mianshijun.xyz
- 服务器部署路径更正：项目实际部署在 /var/www/mianshijun 而不是 /home/<USER>/local-mianshijun。GitHub账户信息：用户名 BasicProtein，密码 ****************************************
- 修复第二次语音识别失败问题：1)后端DashScope会话ID绑定修复-添加currentSessionId跟踪，修复重连后任务重启逻辑，完善task-failed恢复机制；2)前端音频录制消息格式修复-将错误的sendWebSocketMessage(pcmData)改为正确的消息对象格式，确保type/payload/format字段完整；3)集成useInterviewSession hook到MockInterviewSessionPage，添加音频录制界面和转录事件处理；4)修复ControlBar在mock模式显示音频控件；5)严格复刻技术团队代码，解决音频数据发送格式不匹配问题
- AI模拟面试数据库会话创建失败问题：WebSocket连接正常，扣费成功，消息处理正常，但数据库会话写入失败导致面试无法继续。错误日志显示"Session not found in database after 10 retries"，需要检查PostgreSQL连接状态、sessions表结构和权限配置
- AI模拟面试修复完成：新增/api/interviews/start-mock接口实现原子性扣费和数据库会话创建，前端采用两步流程（先API创建记录再WebSocket连接），统一了模拟面试和正式面试的启动逻辑，解决了数据库会话创建失败问题
- 扣费逻辑重复问题：AI模拟面试和AI正式面试存在重复扣费问题，有时扣2次有时扣3次，需要修复扣费逻辑确保每次面试只扣费一次，不改动UI和其他功能
- AI模拟面试时序问题解决方案：通过握手机制修复前端消息发送早于后端监听器设置的赛跑条件。方案包括：1)后端先设置监听器再发送欢迎消息，2)前端等待收到欢迎消息后再发送start_mock_interview消息，3)添加消息监听器机制到WebSocketManager，4)移除固定延迟依赖，实现可靠的同步机制
- AI模拟面试时序问题握手机制修复完成：1)后端webSocketServer.ts调整Mock模式监听器设置顺序-先设置消息监听器再发送system_status欢迎消息，2)前端WebSocketManager.ts添加消息监听器机制-addMessageListener和removeMessageListener方法，集成到onmessage处理，3)前端MockInterviewSessionPage.tsx实现握手机制-监听system_status消息后发送start_mock_interview，移除不可靠的setTimeout延迟，4)消除时序赛跑条件确保消息不丢失，5)保持Live模式不变不影响正式面试功能
